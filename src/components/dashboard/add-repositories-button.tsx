"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Plus, ExternalLink, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { useSession } from "next-auth/react";
import { Alert, AlertDescription, AlertTitle } from "@/src/components/ui/alert";
import { GitHubInstallationError, PermissionError, NetworkError } from "@/src/components/ui/error-boundary";

interface AddRepositoriesButtonProps {
  organizationId?: string;
  organizationType?: 'personal' | 'organization';
  installationStatus?: 'active' | 'pending' | 'suspended' | 'deleted';
  onInstallationStart?: () => void;
  className?: string;
}

export function AddRepositoriesButton({
  organizationId,
  organizationType = 'personal',
  installationStatus,
  onInstallationStart,
  className
}: AddRepositoriesButtonProps) {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInstallGitHubApp = async () => {
    if (!session?.user?.id) {
      setError("Please sign in to install the GitHub app");
      return;
    }

    setIsLoading(true);
    setError(null);
    onInstallationStart?.();

    try {
      // Build query parameters for installation
      const params = new URLSearchParams({
        type: organizationType,
      });

      if (organizationId && organizationType === 'organization') {
        params.append('orgId', organizationId);
      }

      // Get installation URL from our API
      const response = await fetch(`/api/github/install?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        if (response.status === 409) {
          // Installation already exists
          setError("GitHub app is already installed for this organization");
          return;
        }
        throw new Error(data.message || 'Failed to generate installation URL');
      }

      // Redirect to GitHub App installation
      window.location.href = data.installUrl;

    } catch (error) {
      console.error('Error starting GitHub app installation:', error);
      setError(error instanceof Error ? error.message : 'Failed to start installation');
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonContent = () => {
    if (isLoading) {
      return (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Redirecting to GitHub...
        </>
      );
    }

    switch (installationStatus) {
      case 'active':
        return (
          <>
            <CheckCircle className="w-4 h-4 mr-2" />
            Manage Repositories
          </>
        );
      case 'suspended':
        return (
          <>
            <AlertCircle className="w-4 h-4 mr-2" />
            Reactivate GitHub App
          </>
        );
      case 'deleted':
        return (
          <>
            <Plus className="w-4 h-4 mr-2" />
            Reinstall GitHub App
          </>
        );
      default:
        return (
          <>
            <Plus className="w-4 h-4 mr-2" />
            Add Repositories
          </>
        );
    }
  };

  const getButtonVariant = () => {
    switch (installationStatus) {
      case 'active':
        return 'outline';
      case 'suspended':
        return 'destructive';
      default:
        return 'default';
    }
  };

  return (
    <div className="space-y-4">
      <Button
        onClick={handleInstallGitHubApp}
        disabled={isLoading}
        variant={getButtonVariant()}
        className={className}
      >
        {getButtonContent()}
        {installationStatus === 'active' && (
          <ExternalLink className="w-4 h-4 ml-2" />
        )}
      </Button>

      {/* Help text */}

      {/* Error display with smart error handling */}
      {error && (
        <>
          {error.includes('permission') || error.includes('access') ? (
            <PermissionError
              error={error}
              settingsUrl="https://github.com/settings/installations"
              onRetry={() => setError(null)}
            />
          ) : error.includes('network') || error.includes('connection') ? (
            <NetworkError
              error={error}
              onRetry={() => setError(null)}
            />
          ) : error.includes('already installed') ? (
            <GitHubInstallationError
              error={error}
              onRetry={() => setError(null)}
            />
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Installation Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </>
      )}

      {/* Installation status info */}
      {installationStatus === 'active' && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>GitHub App Installed</AlertTitle>
          <AlertDescription>
            The Platyfend GitHub app is successfully installed. You can manage repository 
            access and permissions directly on GitHub.
          </AlertDescription>
        </Alert>
      )}

      {installationStatus === 'suspended' && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Suspended</AlertTitle>
          <AlertDescription>
            GitHub app access has been suspended. Please reactivate the app to continue 
            using repository features.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// Simplified version for quick use
export function QuickAddRepositoriesButton({ 
  className,
  organizationId,
  installationStatus 
}: { 
  className?: string;
  organizationId?: string;
  installationStatus?: 'active' | 'pending' | 'suspended' | 'deleted';
}) {
  return (
    <AddRepositoriesButton
      organizationId={organizationId}
      organizationType="personal"
      installationStatus={installationStatus}
      className={className}
    />
  );
}
